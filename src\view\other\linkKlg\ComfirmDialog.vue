<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import inlineCKEditor from '@/components/editors/VeditorInline.vue';
import { QuestionType, QuestionTypeDict } from '@/utils/constant';
import {
  getLinkListApi,
  params2GetLinkList,
  relationKlgApi,
  params2Relation,
  publishQuesApi,
  rebackQuesApi,
  params2Ques
} from '@/apis/path/klg';
import { onMounted, onUnmounted, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { storeToRefs } from 'pinia';
import { convertImgTagsToMarkdown } from '@/utils/latexUtils';
import { processAllLatexEquations } from '@/utils/latexUtils';

const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);

const selectRef = ref();
const dialogVisible = ref(false);
const curMode = ref(0); //  1: 提问 | 2: 发布问题 | 3: 失效问题 | 4: 生效问题 | 5: 取消发布
const titleText = ref(''); // dialog标题
const contentText = ref(''); // dialog内容
const loading = ref(false);
const selectedList = ref<any[]>([]);
const blankDataForm = {
  klgCode: '',
  associatedWords: '',
  keyword: '',
  sort: 1,
  questionId: null as number | null,
  sourceId: '',
  targetId: [] as string[],
  targetList: [] as any[],
  questionIdList: [] as number[]
};
const dataForm = ref(blankDataForm);
const formRef = ref<FormInstance>();

const isInTargetList = (item: any) => {
  return dataForm.value.targetList.some((targetItem) => targetItem.klgCode === item.klgCode);
};
// 展示dialog
const showDialog = (data: any) => {
  // 完全重置 dataForm，确保没有残留数据
  dataForm.value = { ...blankDataForm };
  dataForm.value.targetId = [];
  dataForm.value.targetList = [];
  curMode.value = data.mode;
  dialogVisible.value = true;

  switch (curMode.value) {
    case 1:
      titleText.value = '提问';
      dataForm.value.associatedWords = data.associatedWords;
      dataForm.value.sourceId = data.klgCode;
      dataForm.value.keyword = data.keyword;
      if (data.answers && data.answers.length === 0) {
        dataForm.value.targetList = [];
      } else if (data.answers && data.answers.length > 0) {
        dataForm.value.targetList = [...data.answers];
      }

      // 明确处理 questionId 的设置逻辑
      if (data.questionId !== undefined && data.questionId !== null) {
        // 编辑已有问题的情况
        dataForm.value.questionId = data.questionId;
        if (data.sort) {
          dataForm.value.sort = data.sort;
        }
      } else {
        // 新建问题的情况，确保 questionId 为 null
        dataForm.value.questionId = null;
        if (data.sort) {
          dataForm.value.sort = data.sort;
        }
      }

      break;
    case 2:
      titleText.value = '发布问题';
      contentText.value = '您确定要发布选中问题吗？';
      dataForm.value.questionIdList = data.list;
      break;
    case 3:
      titleText.value = '失效问题';
      contentText.value = '您确定要取消发布选中问题吗？';
      dataForm.value.questionIdList = data.list;
      break;
    case 4:
      titleText.value = '生效问题';
      break;
    case 5:
      titleText.value = '取消发布';
      break;
  }
};
// 处理选择
const handleSelect = (item: any) => {
  const index = dataForm.value.targetList.findIndex((target) => target.klgCode === item.klgCode);
  if (index !== -1) {
    dataForm.value.targetList.splice(index, 1);
  } else {
    if (dataForm.value.sort === QuestionType.what && dataForm.value.targetList.length > 0) {
      ElMessage.warning('最多选择一个知识点！');
      return;
    }
    if (selectRef.value) {
      selectRef.value.blur();
    }
    dataForm.value.targetList.push(item);
  }
};
// 处理选择类型
const handleClick = () => {
  if (dataForm.value.sort === 1 && dataForm.value.targetList.length > 1) {
    ElMessage.warning('"是什么"只能选择一个关联知识点');
    dataForm.value.sort = 2;
  }
};
// 远程
const getLinkList = (query: string) => {
  const params: params2GetLinkList = {
    keyword: query,
    klgCode: dataForm.value.sourceId
  };
  getLinkListApi(params).then((res) => {
    if (res.success) {
      selectedList.value = res.data.list;
    }
  });
};
// 处理改变类型
const handleChangeSort = () => {
  dataForm.value.targetList = [];
};
// 处理提交
const handleSubmit = () => {
  switch (curMode.value) {
    case 1:
      // 提问或编辑
      formRef.value?.validate((valid) => {
        if (valid) {
          const params1: params2Relation = {
            questionId: dataForm.value.questionId,
            associatedWords: convertImgTagsToMarkdown(dataForm.value.associatedWords),
            keyword: convertImgTagsToMarkdown(dataForm.value.keyword),
            sort: dataForm.value.sort,
            sourceId: dataForm.value.sourceId,
            targetId: dataForm.value.targetList.map((item) => {
              return item.klgCode;
            })
          };

          console.log('🚀 提交问题，questionId =', params1.questionId);
          relationKlgApi(params1).then((res) => {
            if (res.success) {
              ElMessage.success('提交成功');
              drawerControllerStore.mode = false;
              emitter.emit(Event.ADD_QUESTION, res.data);
              // 发送刷新事件，通知PreKlgEditList组件更新表格数据
              emitter.emit(Event.REFRESH_QUESTION);
              dialogVisible.value = false;
            } else {
              ElMessage.error(res.message);
            }
          });
        }
      });
      break;
    case 2:
      // 发布问题
      const params2: params2Ques = {
        questionList: dataForm.value.questionIdList
      };
      publishQuesApi(params2).then((res) => {
        if (res.success) {
          ElMessage.success('发布成功');
          dialogVisible.value = false;
        } else {
          ElMessage.error(res.message);
        }
      });
      break;
    case 3:
      // 失效问题
      const params3: params2Ques = {
        questionList: dataForm.value.questionIdList
      };
      rebackQuesApi(params3).then((res) => {
        if (res.success) {
          ElMessage.success('撤回成功');
          dialogVisible.value = false;
        } else {
          ElMessage.error(res.message);
        }
      });
      break;
    case 4:
      // 生效问题
      break;
    case 5:
      // 取消发布
      break;
  }
};
// 处理删除target
const handleDeleteTarget = (item: any) => {
  dataForm.value.targetList = dataForm.value.targetList.filter((target) => {
    return target.klgCode !== item.klgCode;
  });
};

onMounted(() => {
  emitter.on(Event.SHOW_QUESTION_DIALOG, showDialog);
});

onUnmounted(() => {
  emitter.off(Event.SHOW_QUESTION_DIALOG, showDialog);
});
defineExpose({
  showDialog
});
</script>
<template>
  <el-dialog v-model="dialogVisible" width="800" style="max-height: 800px" :z-index="20">
    <template #header> {{ titleText }} </template>
    <div class="main-container">
      <el-form ref="formRef" :model="dataForm" v-if="curMode === 1">
        <div>问题内容</div>
        <div class="associated-words">
          <span style="margin-right: 10px; white-space: nowrap">文本关联内容: </span>
          <span
            class="words-text ck-content questionList"
            v-html="processAllLatexEquations(dataForm.associatedWords)"
          ></span>
        </div>
        <el-form-item
          style="width: 100%"
          prop="keyword"
          :rules="{
            required: true,
            message: '请输入关键字',
            trigger: 'blur'
          }"
        >
          <inlineCKEditor
            style="width: 100%"
            v-model="dataForm.keyword"
            placeholder="请输入关键词"
            :showToolbar="true"
          ></inlineCKEditor>
        </el-form-item>
        <el-form-item
          prop="sort"
          :rules="{
            required: true,
            message: '请选择类型',
            trigger: 'blur'
          }"
        >
          <el-select
            v-model="dataForm.sort"
            placeholder="请选择类型"
            style="width: 100%"
            @change="handleChangeSort"
          >
            <el-option
              v-for="(key, value) in QuestionTypeDict"
              :key="value"
              :label="value"
              :value="key"
              class="primary"
              @click="handleClick"
            />
          </el-select>
        </el-form-item>
        <div class="line"></div>
        <div>关联查询</div>
        <el-form-item class="link-block">
          <el-select
            ref="selectRef"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请输入要关联的知识"
            :remote-method="getLinkList"
            :loading="loading"
            :remote-show-suffix="true"
            suffix-icon="Search"
            :fit-input-width="true"
          >
            <el-option
              v-for="(item, index) in selectedList"
              :key="index"
              :label="item.title"
              :value="item.title"
              v-html="item.title"
              class="ck-content"
              :class="{ selected: isInTargetList(item) }"
              @click="handleSelect(item)"
            />
          </el-select>
        </el-form-item>
        <div>关联结果</div>
        <span class="link-result">
          <div v-if="dataForm.targetList.length === 0">暂无关联结果</div>
          <span v-else class="link-line" v-for="(item, index) in dataForm.targetList" :key="index">
            <span class="ck-content link-line-content" v-html="item.title"></span>
            <span class="close-btn" @click="handleDeleteTarget(item)"
              ><el-icon><Close /></el-icon
            ></span>
          </span>
        </span>
      </el-form>
      <div v-else>{{ contentText }}</div>
    </div>
    <template #footer>
      <div class="footer">
        <CmpButton type="info" @click="dialogVisible = false">关闭</CmpButton>
        <cmp-button type="primary" @click="handleSubmit"> 确定 </cmp-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
:deep(img) {
  max-width: 100px;
  height: auto;
}
:deep(.ck-content .image-inline img) {
  max-width: 100px;
  height: auto;
}
.main-container {
  height: 600px;
  overflow-x: hidden;
  overflow-y: auto;
  /* 添加自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 6px;
    height: 5px;
  }

  &::-webkit-scrollbar-track {
    background-color: var(--color-theme-hover);
  }
  color: var(--color-black);
  :deep(.el-form-item) {
    margin-bottom: 10px;
  }
  .associated-words {
    display: flex;
    flex-direction: row;
    background-color: var(--color-light);
    padding: 10px;
    border-radius: 3px;
    margin-bottom: 10px;
    .words-text {
      font-weight: 600;
      word-break: break-all;
    }
  }
  .link-block {
    :deep(.el-select__caret.is-reverse) {
      transform: rotate(0deg);
    }
  }
  .link-result {
    width: 100%;
    background-color: var(--color-light);
    border-radius: 3px;
    height: 200px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 3px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 6px;
      height: 50px;
    }
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
    }
    .link-line {
      width: 100%;
      background-color: white;
      border-radius: 3px;
      border: 1px solid var(--color-boxborder);
      margin-bottom: 5px;
      padding: 5px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .link-line-content {
        display: flex;
        align-items: center;
      }
      .close-btn {
        padding: 5px 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
      }
    }
  }
}
.footer {
  display: flex;
  justify-content: center;
  gap: 40px;
}
.line {
  margin: 10px 0;
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.selected {
  color: var(--color-primary);
  font-weight: 600;
  background-color: var(--color-light);
}
:deep(p) {
  margin: 0;
}
</style>
<style>
.primary {
  --el-color-primary: var(--color-primary);
}
</style>
