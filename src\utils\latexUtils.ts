/**
 * 处理字符串中的所有类型LaTeX公式（包括行内公式$...$和行间公式$$...$$），并用适当的元素包裹
 * @param {string} text - 包含LaTeX公式的输入文本
 * @returns {string} 处理后的文本，其中LaTeX公式被适当的元素包裹
 */
import { renderMarkdown } from '@/utils/markdown';
import URLParse from 'url-parse';
import { decodeHTML } from 'entities';
export function convertLanguageMathToScript(text: string): string {
  if (!text) return '';

  let result = text;

  // 处理块级公式 <div class="language-math">公式</div>
  // 使用非贪婪匹配和多行模式
  const divRegex = /<div\s+class="language-math"[^>]*>(.*?)<\/div>/gs;
  result = result.replace(divRegex, (_, formula) => {
    // 去除公式内容两端的空白字符
    const cleanFormula = decodeHTML(formula.trim());

    return `<script type="math/tex; mode=display">${cleanFormula}</script>`;
  });

  // 处理行内公式 <span class="language-math">公式</span>
  // 使用非贪婪匹配
  const spanRegex = /<span\s+class="language-math"[^>]*>(.*?)<\/span>/g;
  result = result.replace(spanRegex, (_, formula) => {
    // 去除公式内容两端的空白字符
    const cleanFormula = formula.trim();
    return `<script type="math/tex">${cleanFormula}</script>`;
  });

  return result;
}
export function convertImgTagLongUrls(content: string): string {
  if (!content) return '';

  let result = content;

  // 匹配HTML img标签，捕获src属性值
  // 支持单引号、双引号和无引号的情况
  const imgTagRegex = /<img\s+([^>]*?)src\s*=\s*(['"]?)([^'">\s]+)\2([^>]*?)>/gi;

  result = result.replace(
    imgTagRegex,
    (match: string, beforeSrc: string, quote: string, srcUrl: string, afterSrc: string) => {
      try {
        // 去掉URL两端的空格
        srcUrl = srcUrl.trim();

        // 检查是否是完整的HTTP/HTTPS URL
        if (!srcUrl.startsWith('http://') && !srcUrl.startsWith('https://')) {
          // 如果不是完整URL，保持原样
          return match;
        }

        // 使用URLParse解析URL
        const parsedUrl = new URLParse(srcUrl);

        // 获取pathname
        let pathname = parsedUrl.pathname;

        // 去掉开头的斜杠
        if (pathname.startsWith('/')) {
          pathname = pathname.substring(1);
        }

        // 如果pathname为空或只是斜杠，尝试从完整URL中提取文件名
        if (!pathname || pathname === '') {
          const urlParts = srcUrl.split('/');
          if (urlParts.length > 0) {
            const lastPart = urlParts[urlParts.length - 1];
            // 去掉查询参数和fragment
            pathname = lastPart.split('?')[0].split('#')[0];
          }
        }

        // 如果还是没有有效路径，保持原样
        if (!pathname || pathname === '') {
          return match;
        }

        // 重新构建img标签，保持其他属性不变
        return `<img ${beforeSrc}src${quote ? `=${quote}${pathname}${quote}` : `=${pathname}`}${afterSrc}>`;
      } catch (error) {
        console.error('转换img标签长链接时出错:', error);
        // 出错时返回原始内容
        return match;
      }
    }
  );

  return result;
}
export function encodeHTML(str: string) {
  // 提取所有img标签，包括自闭合和非自闭合形式
  const imgTags: string[] = [];
  const imgRegex = /<img[^>]*\/?>/gi;

  // 用占位符替换img标签，并保存原始标签
  const processedStr = str.replace(imgRegex, (match) => {
    const index = imgTags.length;
    imgTags.push(match);
    return `__IMG_PLACEHOLDER_${index}__`;
  });

  // 创建一个临时的div元素
  const tempDiv = document.createElement('div');

  // 将处理后的字符串设置为div的文本内容，此时< >等字符被当作纯文本
  tempDiv.textContent = processedStr;

  // 读取div的innerHTML，浏览器会自动返回编码后的HTML字符串
  let encodedHTML = tempDiv.innerHTML;

  // 将占位符替换回原始的img标签
  imgTags.forEach((imgTag, index) => {
    encodedHTML = encodedHTML.replace(`__IMG_PLACEHOLDER_${index}__`, imgTag);
  });

  return encodedHTML;
}

function processAllLatexEquations(text: string) {
  if (!text) return '';

  try {
    // 首先，查找并处理行间公式 ($$...$$)
    const displayMatches = [];
    // 使用更健壮的正则表达式，更好地处理多行和复杂环境如align
    const displayRegex = /\$\$([\s\S]*?)\$\$/g; // 使用[\s\S]*?代替.*?以更好地处理多行
    let displayMatch: RegExpExecArray | null;

    while ((displayMatch = displayRegex.exec(text)) !== null) {
      displayMatches.push({
        start: displayMatch.index,
        end: displayMatch.index + displayMatch[0].length,
        content: displayMatch[1].trim()
      });
    }

    // 从后向前处理行间公式匹配项
    let result = text;
    for (let i = displayMatches.length - 1; i >= 0; i--) {
      let { start, end, content } = displayMatches[i];

      try {
        // 清理LaTeX内容，处理特殊字符
        content = cleanLatexContent(content);

        const mathString = `$$${content}$$`;
        // console.log("[latexUtils] 行间公式原始内容:", content);
        // console.log("[latexUtils] 行间公式传给renderMarkdown的内容:", mathString);
        const renderedContent = renderMarkdown(mathString);
        // console.log("[latexUtils] 行间公式渲染后的内容:", renderedContent);

        // 处理renderedContent中可能存在的段落标签
        let processedContent = renderedContent;
        // 移除开头和结尾的<p>标签
        processedContent = processedContent.replace(/^\s*<p>|<\/p>\s*$/g, '');

        const replacement = `<div class="equation" latexcode="${content}" >${processedContent}</div>`;
        result = result.substring(0, start) + replacement + result.substring(end);
      } catch (err) {
        console.error('[latexUtils] 处理行间公式时出错:', err);
        // 出错时保留原始公式
        const replacement = `<div class="equation">${content}</div>`;
        result = result.substring(0, start) + replacement + result.substring(end);
      }
    }

    // 然后查找并处理行内公式 ($...$)，排除那些是行间公式一部分的内容
    const inlineMatches = [];
    const inlineRegex = /\$(.*?)\$/g;
    let inlineMatch: RegExpExecArray | null;

    while ((inlineMatch = inlineRegex.exec(result)) !== null) {
      // 如果这是行间公式($$...$$)的一部分，则跳过
      const prevChar = result.charAt(inlineMatch.index - 1);
      const nextChar = result.charAt(inlineMatch.index + inlineMatch[0].length);
      if (prevChar === '$' || nextChar === '$') continue;

      inlineMatches.push({
        start: inlineMatch.index,
        end: inlineMatch.index + inlineMatch[0].length,
        content: inlineMatch[1].trim()
      });
    }

    // 从后向前处理行内公式匹配项
    for (let i = inlineMatches.length - 1; i >= 0; i--) {
      let { start, end, content } = inlineMatches[i];

      try {
        // 清理LaTeX内容，处理特殊字符
        content = cleanLatexContent(content);

        const mathString = `$${content}$`;
        // console.log("[latexUtils] 行内公式传给renderMarkdown的内容:", mathString);
        const renderedContent = renderMarkdown(mathString);
        // console.log("[latexUtils] 行内公式渲染后的内容:", renderedContent);

        // 处理renderedContent中可能存在的换行符和段落标签
        let processedContent = renderedContent;
        // 移除开头和结尾的<p>标签
        processedContent = processedContent.replace(/^\s*<p>|<\/p>\s*$/g, '');
        // 移除所有的换行符
        processedContent = processedContent.replace(/\n/g, '');

        // 保存规范化后的LaTeX代码，以便在提问模式下使用和匹配

        const replacement = `<span class="inline-equation" latexcode="${content}">${processedContent}</span>`;
        result = result.substring(0, start) + replacement + result.substring(end);
      } catch (err) {
        console.error('[latexUtils] 处理行内公式时出错:', err);
        // 出错时保留原始公式
        const replacement = `<span class="inline-equation">${content}</span>`;
        result = result.substring(0, start) + replacement + result.substring(end);
      }
    }

    // 最后渲染整个文档
    try {
      return renderMarkdown(result);
    } catch (err) {
      console.error('[latexUtils] 渲染最终文档时出错:', err);
      return result; // 出错时返回部分处理的结果
    }
  } catch (err) {
    console.error('[latexUtils] 处理LaTeX公式时出错:', err);
    // 出错时返回原始文本
    return text;
  }
}

/**
 * 处理字符串中的LaTeX公式，只提取公式内容而不进行渲染
 * 这个函数适用于当renderMarkdown函数不能正确渲染LaTeX公式的情况
 * @param {string} text - 包含LaTeX公式的输入文本
 * @returns {string} 处理后的文本，其中LaTeX公式被适当的元素包裹
 */

/**
 * 将文本中的数学标签转换为LaTeX格式
 * 将<script type="math/tex">...</script>转换为$...$
 * 将<script type="math/tex; mode=display">...</script>转换为$$...$$
 * @param text - 包含数学标签的输入文本
 * @returns 转换后的文本，其中数学标签被转换为LaTeX格式
 */
function convertMathTagsToMDLatex(text: string): string {
  if (!text) return '';

  let result = text;

  // 转换行间公式 <script type="math/tex; mode=display">...</script> 为 $$...$$
  const displayRegex = /<script type="math\/tex; mode=display">(.*?)<\/script>/gs;
  result = result.replace(displayRegex, (_match: string, content: string) => {
    return `$$${content}$$`;
  });

  // 转换行内公式 <script type="math/tex">...</script> 为 $...$
  const inlineRegex = /<script type="math\/tex">(.*?)<\/script>/gs;
  result = result.replace(inlineRegex, (_match: string, content: string) => {
    return `$${content}$`;
  });

  return result;
}

/**
 * 将HTML图片标签转换为Markdown格式的图片链接
 * 例如：<img src="https://example.com/path/filename.jpg" alt="filename.jpg">
 * 转换为：![filename.jpg](path/filename.jpg)
 * @param {string} text - 包含HTML图片标签的输入文本
 * @returns {string} 转换后的文本，其中HTML图片标签被转换为Markdown格式
 */
function convertImgTagsToMarkdown(text: string): string {
  if (!text) return '';

  let result = text;

  // 匹配HTML图片标签，处理带引号和不带引号的情况
  const imgRegex =
    /<img\s+src=(?:\\?"|')?([^"'>]+)(?:\\?"|')?(?:\s+alt=(?:\\?"|')?([^"'>]*)(?:\\?"|')?)?[^>]*>/g;

  result = result.replace(imgRegex, (_match: string, src: string, alt: string = '') => {
    // 解码HTML实体
    src = src.replace(/&amp;/g, '&');

    try {
      // 提取日期和文件ID部分 - 匹配格式：2025-04-27/987bf988ded54be7aabb81e985f23f78_infinity2481958.jpg
      const dateFilePattern = /(\d{4}-\d{2}-\d{2}\/[a-f0-9]+_[^/?]+)/;
      const dateMatch = src.match(dateFilePattern);

      let path = '';

      if (dateMatch && dateMatch[1]) {
        // 如果找到日期和文件ID格式的部分，使用它
        path = dateMatch[1];
      } else {
        // 如果没有找到日期格式，尝试提取文件名
        const urlParts = src.split('/');
        if (urlParts.length > 0) {
          const lastPart = urlParts[urlParts.length - 1].split('?')[0];
          path = lastPart;
        }
      }

      // 如果还是没有提取到路径，使用整个src
      if (!path) {
        path = src.split('?')[0];
      }

      // 使用alt作为显示文本，如果没有alt则使用文件名
      const fileName = path.split('/').pop() || '';
      const displayText = alt || fileName || 'image';

      // 返回Markdown格式的图片链接
      return `![${displayText}](${path})`;
    } catch (error) {
      console.error('转换图片标签时出错:', error);
      return _match; // 出错时返回原始标签
    }
  });

  return result;
}
/**
 * 规范化LaTeX公式内容，确保处理一致性
 * 处理反斜杠和HTML实体编码，使公式能够正确渲染
 * @param content LaTeX公式内容
 * @returns 规范化后的内容
 */
function cleanLatexContent(content: string): string {
  if (!content) return '';

  let cleanContent = content;

  // 1. 处理HTML实体编码
  // 将&amp;转换回&符号，这是关键步骤，确保HTML实体在传递给KaTeX前被正确解码
  cleanContent = cleanContent.replace(/&amp;/g, '&');

  // 2. 处理末尾的任意数量的反斜杠+任意空白字符
  cleanContent = cleanContent.replace(/\\[\s\n]*$/g, ''); // 3. 处理矩阵和align环境中的行结束符
  // 确保矩阵和align环境中的\\被正确处理，避免被错误解析为单个反斜杠
  // 在pmatrix、bmatrix等环境中，\\用于表示行结束
  cleanContent = cleanContent.replace(
    /(\\begin\{[a-z]*matrix\}[\s\S]*?)(\\\\)([\s\S]*?\\end\{[a-z]*matrix\})/g,
    (_, before, lineBreak, after) => {
      // 保持\\作为行结束符
      return before + lineBreak + after;
    }
  );

  // 处理align环境中的行结束符
  cleanContent = cleanContent.replace(
    /(\\begin\{align\}[\s\S]*?)(\\\\)([\s\S]*?\\end\{align\})/g,
    (_, before, lineBreak, after) => {
      // 保持\\作为行结束符
      return before + lineBreak + after;
    }
  );

  // 4. 处理换行符
  // 移除公式开头和结尾的换行符，保留公式内部的换行符
  cleanContent = cleanContent.replace(/^\n+|\n+$/g, '');

  // 5. 规范化LaTeX命令中的反斜杠
  // 将多个连续反斜杠(\\\)规范化为单个反斜杠(\)
  // 这是关键步骤，确保无论输入中有多少个反斜杠，都规范化为正确的数量

  // 先处理命令前的反斜杠：确保命令前只有一个反斜杠
  cleanContent = cleanContent.replace(
    /\\{2,}(?=begin|end|left|right|mathrm|frac|sum|int|lim|infty|partial|nabla|alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega)/g,
    '\\'
  );

  // 6. 处理下划线和大括号前的反斜杠
  // 在LaTeX中，\_表示文本下划线，而_表示下标
  // 在匹配时，我们希望统一使用_作为下标
  cleanContent = cleanContent.replace(/\\_/g, '_');
  cleanContent = cleanContent.replace(/\\{/g, '{');
  cleanContent = cleanContent.replace(/\\}/g, '}');

  // 7. 处理注释
  // 移除LaTeX注释行（以%开头的内容）
  cleanContent = cleanContent.replace(/\s*%.*$/gm, '');

  // 保留其他必要的反斜杠，如\prime, \cdot等

  return cleanContent;
}

function test() {
  console.log('test2', cleanLatexContent('some text \\'));
}
test();
function convertMathFormulas(text: string): string {
  if (!text) return '';

  // 查找所有 <code> 标签的位置（包括自闭合和成对标签）
  const codeRanges: Array<{ start: number; end: number }> = [];

  // 匹配 <code>...</code> 成对标签
  const codeRegex = /<code[^>]*>.*?<\/code>/gs;
  let match;
  while ((match = codeRegex.exec(text)) !== null) {
    codeRanges.push({
      start: match.index,
      end: match.index + match[0].length
    });
  }

  // 检查位置是否在 code 标签内
  function isInCodeTag(position: number): boolean {
    return codeRanges.some((range) => position >= range.start && position < range.end);
  }

  // 处理块级公式 $$公式$$，但跳过 code 标签内的内容
  let result = text.replace(/\$\$\s*(.*?)\s*\$\$/gs, (match, formula, offset) => {
    // 检查这个匹配是否在 code 标签内
    if (isInCodeTag(offset)) {
      return match; // 保持原样，不转换
    }
    return `<script type="math/tex; mode=display">${formula}</script>`;
  });

  // 重新计算 code 标签位置（因为上面的替换可能改变了文本长度）
  codeRanges.length = 0;
  const updatedCodeRegex = /<code[^>]*>.*?<\/code>/gs;
  while ((match = updatedCodeRegex.exec(result)) !== null) {
    codeRanges.push({
      start: match.index,
      end: match.index + match[0].length
    });
  }

  // 处理行内公式 $公式$，但跳过 code 标签内的内容
  result = result.replace(
    /(?<!\$)\$(?!\$)\s*((?:[^$\n]|\\\$)+?)\s*\$(?!\$)/g,
    (match, formula, offset) => {
      // 检查这个匹配是否在 code 标签内
      if (isInCodeTag(offset)) {
        return match; // 保持原样，不转换
      }
      return `<script type="math/tex">${formula}</script>`;
    }
  );

  return result;
}

// 导出函数
export {
  processAllLatexEquations,
  convertMathTagsToMDLatex,
  convertImgTagsToMarkdown,
  cleanLatexContent,
  convertMathFormulas
};
