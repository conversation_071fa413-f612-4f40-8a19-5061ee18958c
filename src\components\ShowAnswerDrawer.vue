<script setup lang="ts">
import { emitter } from '@/utils/emitter';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { Event } from '@/types/event';

// 定义 props
const props = defineProps<{
  visible?: boolean;
  questionData?: any;
}>();

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

const drawerVisible = ref(false);
const dataForm = ref();

// 监听 props 变化
watch(
  () => props.visible,
  (newVal) => {
    drawerVisible.value = !!newVal;
  }
);

watch(
  () => props.questionData,
  (newVal) => {
    if (newVal) {
      dataForm.value = newVal;
    }
  }
);

// 监听抽屉关闭事件
watch(drawerVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 跳转到klgDetail
const toKlgDetail = (item: any) => {
  window.open(`/klgdetail?klgCode=${item.klgCode}`);
};

// 展示drawer（保留原有的 emitter 方式）
const showDrawer = (data: any) => {
  dataForm.value = data;
  drawerVisible.value = true;
};

// 处理自定义事件显示抽屉
const handleShowFromFloating = (event: CustomEvent) => {
  const { question } = event.detail;
  if (question) {
    showDrawer(question);
  } else {
    console.warn('⚠️ showAnswerFromFloating 事件中没有 question 数据');
  }
};

onMounted(() => {
  emitter.on(Event.SHOW_DRAWER, showDrawer);
  // 监听从浮动弹窗显示答案抽屉的事件
  window.addEventListener('showAnswerFromFloating', handleShowFromFloating as EventListener);
});

onUnmounted(() => {
  emitter.off(Event.SHOW_DRAWER, showDrawer);
  window.removeEventListener('showAnswerFromFloating', handleShowFromFloating as EventListener);
});
</script>
<template>
  <el-drawer v-model="drawerVisible" direction="ltr" size="50%">
    <template #header>
      <div class="header-text">显示问题</div>
    </template>
    <div class="wrapper">
      <el-form>
        <el-form-item>
          <div class="associated-words-block">
            <span style="font-weight: 600; white-space: nowrap">文本关联内容:</span
            ><span
              class="ck-content"
              v-html="dataForm?.associatedWords || ''"
              style="margin-left: 10px; word-break: break-all"
            ></span>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="keyword-block">
            【<span v-html="dataForm?.keyword || ''" class="keyword ck-content"></span>】<span
              style="margin-left: 10px"
              >{{ dataForm?.questionType || '' }}?</span
            >
          </div>
        </el-form-item>
      </el-form>
      <el-form-item label="关联结果"> </el-form-item>
      <el-form-item>
        <div class="result-block">
          <span v-if="!dataForm || !dataForm.answers || dataForm.answers.length === 0">暂无</span>
          <span v-else>
            <span v-for="item in dataForm.answers" :key="item.klgCode || item.id">
              <span class="result-block-item" @click="toKlgDetail(item)" style="cursor: pointer">
                <span class="ck-content" v-html="item.title" style="margin-left: 10px"></span>
                <img src="@/assets/image/klg/u1705.svg" height="12" style="margin-right: 12px" />
              </span>
            </span>
          </span>
        </div>
      </el-form-item>
    </div>
  </el-drawer>
</template>
<style scoped>
:deep(.el-form-item) {
  margin: 0;
}
.header-text {
  color: var(--color-black);
  font-weight: 600;
}
.wrapper {
  font-size: 14px;
  color: var(--color-black);
  .associated-words-block {
    width: 100%;
    padding: 0 20px;
    background-color: var(--color-light);
    :deep(p) {
      display: inline;
      margin: 0;
    }
  }
  .keyword-block {
    .keyword {
      font-weight: 700;
      word-break: break-all;
    }
    :deep(p) {
      display: inline-block;
    }
  }
  .result-block {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    width: 100%;
    background-color: var(--color-light);
    .result-block-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      background-color: white;
      margin-bottom: 5px;
      border: 1px solid var(--color-boxborder);
      :deep(p) {
        margin: 0;
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
<style>
.el-drawer__header {
  margin-bottom: 0px;
}
</style>
