import { ref } from 'vue';

// 处理论证块
/**
 * 处理证明块列表
 * @param {object[]} proofList 证明块列表
 * @returns {string[]} 证明块html列表
 */
export function handleProof(proofList: any[]): string[] {
  const tempProofList: any[] = [];
  // 遍历每个证明块
  proofList.forEach((block: any) => {
    // 遍历每个证明块中的每个条件
    block.klgProofCondList.forEach((cond: any) => {
      // 生成html
      tempProofList.push(
        `<span blockid="${block.klgProofBlockId}" condid="${cond.klgProofCondId}" sortid="${cond.sort}">${cond.cnt}</span>`
      );
    });
    // 生成html
    tempProofList.push(`<span blockid="${block.klgProofBlockId}">${block.conclusion}</span>`);
  });
  return tempProofList;
}

// 处理习题
export const handleExercise = (exercise: any): string[] => {
  const tempExerciseList = [];
  tempExerciseList.push(
    `<span etype="stem" type="${exercise.type}" style="width: 100%;">${exercise.stem}</span>`
  );
  if (exercise.content) {
    exercise.content.forEach((item) => {
      tempExerciseList.push(
        `<span etype="content" contentid="${item.optionId}">${item.text}</span>`
      );
    });
  }
  tempExerciseList.push(`<span etype="answer">${exercise.answer}</span>`);
  tempExerciseList.push(`<span etype="explanation">${exercise.explanation}</span>`);
  return tempExerciseList;
};

// 把list转化为exercise
export const transferList2Exercise = (list: any[]): any => {
  const tempExercise = ref<{
    type: number;
    stem: string;
    content: any[];
    answer: string;
    explanation: string;
  }>({
    type: 0,
    stem: '',
    content: [],
    answer: '',
    explanation: ''
  });
  const tempList = ref<any[]>([]);
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素
    if (span && span.hasAttribute('etype')) {
      const etype = span.getAttribute('etype');
      if (span.hasAttribute('type')) {
        const type = span.getAttribute('type');
        if (type) {
          tempExercise.value.type = parseInt(type);
        }
      }
      if (etype === 'content') {
        const op = {
          optionId: span.getAttribute('contentid'),
          text: item
        };
        tempList.value.push(op);
      } else {
        tempExercise.value[etype] = item;
      }
    }
  });
  tempExercise.value.content = tempList.value;
  return tempExercise.value;
};

// 把worker的list转化为exercise
export const transferList2ProofList = (list: any[]): any => {
  const tempList = ref<any[]>([]);
  let tempCondList: any[] = [];
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素

    if (span && span.hasAttribute('condid') && span.getAttribute('condid')?.trim()) {
      const sort = span.getAttribute('sortid')?.trim();
      let newBlankCond: any = {
        cnt: item.toString()
      };
      if (sort) {
        newBlankCond.sort = parseInt(sort);
      }
      tempCondList.push(newBlankCond);
    } else {
      const newBlankBlock: any = {
        conclusion: item.toString(),
        klgProofCondList: tempCondList
      };
      tempList.value.push(newBlankBlock);
      tempCondList = [];
    }
  });

  return tempList;
};

/**
 * 将处理后的content数组转换为完整的证明过程HTML结构
 * @param {string[]} contentArray 处理后的内容数组
 * @returns {string} 完整的证明过程HTML结构
 */
export function convertContentToProofHTML(contentArray: string[]): string {
  if (!contentArray || contentArray.length === 0) {
    return '<div class="proof-block-list">暂无证明过程</div>';
  }

  let html = '<div class="proof-block-list">';
  let currentBlock: string[] = [];
  let blockIndex = 0;

  contentArray.forEach((item) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span');

    if (span) {
      const hasCondId = span.hasAttribute('condid') && span.getAttribute('condid')?.trim();

      if (hasCondId) {
        // 这是一个条件项
        currentBlock.push(item);
      } else {
        // 这是一个结论项，需要结束当前块
        if (currentBlock.length > 0 || item) {
          // 开始新的证明块
          html += `<div class="proof-block">`;

          // 添加所有条件项
          currentBlock.forEach((condItem) => {
            html += `<div class="proof-block-item">`;
            html += `<span class="proof-item-content">${condItem}</span>`;
            html += `</div>`;
          });

          // 添加结论项
          if (item) {
            html += `<div class="proof-block-item">`;
            html += `<span class="proof-item-content">${item}</span>`;
            html += `</div>`;
          }

          html += `</div>`; // 结束证明块

          // 重置当前块
          currentBlock = [];
          blockIndex++;
        }
      }
    }
  });

  // 处理最后一个块（如果存在未处理的条件）
  if (currentBlock.length > 0) {
    html += `<div class="proof-block">`;
    currentBlock.forEach((condItem) => {
      html += `<div class="proof-block-item">`;
      html += `<span class="proof-item-content">${condItem}</span>`;
      html += `</div>`;
    });
    html += `</div>`;
  }

  html += '</div>';
  return html;
}
